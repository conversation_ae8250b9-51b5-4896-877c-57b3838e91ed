from langchain_community.vectorstores import FAISS
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
import langdetect
import re

from langgraph.graph import StateGraph
from typing import TypedDict, Optional, List, Any
from code_review_agent.utils.tools import scan_structure, check_naming, check_syntax, review_logic, summarize_issues

# def extract_ast_chunks(code_text):
#     chunks = []
#     try:
#         tree = ast.parse(code_text)
#         lines = code_text.splitlines()
#         for node in tree.body:
#             if hasattr(node, 'lineno') and hasattr(node, 'end_lineno'):
#                 start = node.lineno - 1
#                 end = node.end_lineno
#                 chunks.append('\n'.join(lines[start:end]))
#     except Exception as e:
#         print("AST parsing failed:", e)
#     return chunks

def chunk_diff(diff_text, language_hint=None):
    # ast_chunks = extract_ast_chunks(diff_text)
    # if ast_chunks:
    #     return RecursiveCharacterTextSplitter(chunk_size=512, chunk_overlap=128).create_documents(ast_chunks)
    #
    pattern = re.compile(r'(def|function|class|public|private|protected)\s+[\w<>,\s\[\]]+\s+[\w_]+\s*\(.*?\)')
    method_starts = [m.start() for m in pattern.finditer(diff_text)] + [len(diff_text)]
    chunks = [diff_text[method_starts[i]:method_starts[i+1]] for i in range(len(method_starts)-1)]
    return RecursiveCharacterTextSplitter(chunk_size=512, chunk_overlap=128).create_documents(chunks)

def build_vector_store(documents):
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")
    return FAISS.from_documents(documents, embeddings)

def detect_language(state):
    try:
        language = langdetect.detect(state["diff"])
        return {"language": language}
    except Exception as e:
        # Default to Python if language detection fails
        return {"language": "python"}

def chunk(state):
    try:
        docs = chunk_diff(state["diff"], language_hint=state["language"])
        vs = build_vector_store(docs)
        return {"chunks": docs, "vs": vs}
    except Exception as e:
        # Return empty chunks and vector store if chunking fails
        return {"chunks": [], "vs": None}

def scan(state):
    return {"scan": scan_structure(state["diff"])}

def naming(state):
    return {"naming": check_naming(state["diff"], state.get("scan"))}

def syntax(state):
    return {"syntax": check_syntax(state["diff"], state.get("language", "python"))}

def review(state):
    try:
        if state.get("vs") is None:
            # If vector store is None, review without context
            return {"review": review_logic(state["diff"], [])}
        retrieved = state["vs"].similarity_search(state["diff"])
        return {"review": review_logic(state["diff"], [d.page_content for d in retrieved])}
    except Exception as e:
        # Fallback to review without context
        return {"review": review_logic(state["diff"], [])}

def summarize(state):
    return {"summary": summarize_issues(state["diff"], [state.get("naming"), state.get("syntax"), state.get("review")])}


def define_langgraph():
    class ReviewState(TypedDict):
        diff: str
        language: Optional[str]
        chunks: Optional[List[Any]]
        vs: Optional[Any]
        scan: Optional[List[dict]]
        naming: Optional[dict]
        syntax: Optional[dict]
        review: Optional[dict]
        summary: Optional[dict]

    graph = StateGraph(ReviewState)

    # Add nodes to the graph (using different names to avoid conflicts with state keys)
    graph.add_node("detect_language_node", detect_language)
    graph.add_node("chunk_node", chunk)
    graph.add_node("scan_node", scan)
    graph.add_node("naming_node", naming)
    graph.add_node("syntax_node", syntax)
    graph.add_node("review_node", review)
    graph.add_node("summarize_node", summarize)

    # Set entry point and edges
    graph.set_entry_point("detect_language_node")
    graph.add_edge("detect_language_node", "chunk_node")
    graph.add_edge("chunk_node", "scan_node")
    graph.add_edge("scan_node", "naming_node")
    graph.add_edge("naming_node", "syntax_node")
    graph.add_edge("syntax_node", "review_node")
    graph.add_edge("review_node", "summarize_node")
    graph.set_finish_point("summarize")

    return graph.compile()