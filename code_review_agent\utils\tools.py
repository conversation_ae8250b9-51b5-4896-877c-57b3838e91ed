from langchain_core.tools import tool
import ast
import re
import langdetect
from code_review_agent.utils.llm import load_local_llm, invoke_with_system_prompt
from code_review_agent.utils.prompts import SYSTEM_PROMPT
import json

@tool
def scan_structure(input: str) -> list:
    """Scan code structure to identify classes, functions, and variables."""
    patterns = [
        (r'^\s*(class|record|interface|struct)\s+(\w+)', 2),
        (r'^\s*(def|function)\s+(\w+)', 2),
        (r'\b(?:public|private|protected|internal|static|async|final)?\s*\w+[<>,\s\[\]]*\s+(\w+)\s*\(.*?\)\s*\{?', 1),
        (r'^\s*def\s+(\w+)\s*\(', 1),
        # Variable declarations with types (C#/Java/TS)
        (r'^\s*(?:var|int|float|double|bool|string|char|long|decimal|const|let|final)\s+(\w+)\s*=.*;', 1),
        # TypeScript/JS/Java field/property/assignment
        # (r'^\s*(\w+)\s*=\s*.+;', 1),
    ]
    result = []
    lines = input.splitlines()
    for i, line in enumerate(lines):
        for pattern, group_index in patterns:
            match = re.search(pattern, line)
            if match:
                name = match.group(group_index)
                if name:
                    result.append({"name": name, "line": i + 1})
    return result

@tool
def check_naming(diff_text: str, declarations: list = None) -> dict:
    """Check naming conventions for declarations in the code"""
    if not declarations:
        return {
            "lines": [],
            "summary": "No declarations found to check"
        }

    decl_str = "\n".join([f"- {d['name']} (line {d['line']})" for d in declarations])

    # Skip language detection for short strings to avoid errors
    try:
        if len(decl_str.strip()) < 10 or langdetect.detect(decl_str) != "en":
            return {
                "lines": [],
                "summary": "\u26a0\ufe0f Naming check skipped: non-English or insufficient code"
            }
    except:
        # If language detection fails, continue with the check
        pass

    llm = load_local_llm()
    user_prompt = f"""Review the following class, method, and variable names for proper naming conventions.
Check for clarity, spelling, meaningful verbs (e.g., is_*/has_* for booleans), and consistency.
Respond in JSON:
{{
"lines": [
    {{"line": number, "feedback": "..."}},
    ...
],
"summary": "..."
}}

Names to review:
{decl_str}"""

    try:
        response = invoke_with_system_prompt(llm, SYSTEM_PROMPT, user_prompt).strip()
        # Clean response if it has markdown formatting
        if response.startswith("```json"):
            response = response.replace("```json", "").replace("```", "").strip()
        return json.loads(response)
    except json.JSONDecodeError as e:
        return {
            "lines": [],
            "summary": f"\u274c Failed to parse naming feedback JSON: {str(e)}"
        }
    except Exception as e:
        return {
            "lines": [],
            "summary": f"❌ Failed to check naming conventions: {str(e)}"
        }


@tool
def check_syntax(diff_text: str, language: str = "python") -> dict:
    """Check code syntax for errors and issues."""
    llm = load_local_llm()
    user_prompt = f"""Check the following {language} code for syntax errors.
Respond in JSON format:
{{
  "valid": boolean,
  "summary": "...",
  "lines": [
    {{"line": number, "feedback": "..."}},
    ...
  ]
}}

Code:
{diff_text}"""

    try:
        response = invoke_with_system_prompt(llm, SYSTEM_PROMPT, user_prompt).strip()
        # Clean response if it has markdown formatting
        if response.startswith("```json"):
            response = response.replace("```json", "").replace("```", "").strip()
        return json.loads(response)
    except json.JSONDecodeError as e:
        return {"valid": False, "summary": f"❌ Failed to parse syntax check JSON: {str(e)}", "lines": []}
    except Exception as e:
        return {"valid": False, "summary": f"\u274c Failed to check syntax: {str(e)}", "lines": []}

@tool
def review_logic(diff_text: str, context: list) -> dict:
    """Review code logic for potential issues and improvements."""
    llm = load_local_llm()
    user_prompt = f"""Review the following code for logic issues.
Refer to context from similar code when useful.
Return JSON format:
{{
  "lines": [
    {{"line": number, "feedback": "..."}},
    ...
  ],
  "summary": "..."
}}

Code:
{diff_text}

Context:
{context}"""

    try:
        response = invoke_with_system_prompt(llm, SYSTEM_PROMPT, user_prompt).strip()
        # Clean response if it has markdown formatting
        if response.startswith("```json"):
            response = response.replace("```json", "").replace("```", "").strip()
        return json.loads(response)
    except json.JSONDecodeError as e:
        return {"lines": [], "summary": f"❌ Failed to parse logic review JSON: {str(e)}"}
    except Exception as e:
        return {"lines": [], "summary": f"\u274c Failed to review logic: {str(e)}"}

@tool
def summarize_issues(diff_text: str, feedbacks: list) -> dict:
    """Summarize all review feedback into a comprehensive report."""
    llm = load_local_llm()
    extracted_summaries = [f.get("summary") if isinstance(f, dict) else str(f) for f in feedbacks if f]

    user_prompt = f"""Analyze the following pull request diff and summarize:
1. What the PR is trying to do (its main purpose).
2. Provide a high-level evaluation: is the implementation clear? any smells?
3. Then summarize the individual review feedbacks below.

Respond with a natural language paragraph.

[DIFF]
{diff_text}

[FEEDBACKS]
{json.dumps(extracted_summaries, indent=2)}"""

    try:
        summary_text = invoke_with_system_prompt(llm, SYSTEM_PROMPT, user_prompt).strip()
        return {
            "summary": summary_text,
            "details": extracted_summaries
        }
    except Exception as e:
        return {
            "summary": f"\u274c Failed to summarize issues: {str(e)}",
            "details": extracted_summaries
        }
